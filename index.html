<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>The Wolf Challenge - CTF Platform</title>

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto+Mono:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Firebase SDK v9 -->
  <script type="module" src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app.js"></script>
  <script type="module" src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js"></script>
  <script type="module" src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js"></script>

  <link rel="stylesheet" href="./style.css">
</head>
<body class="bg-gray-100 min-h-screen">
  <!-- Loading Screen -->
  <div id="loading-screen" class="fixed inset-0 bg-black text-white flex items-center justify-center z-50">
    <div class="text-center">
      <div class="text-6xl mb-4">🐺</div>
      <h1 class="text-4xl font-bold mb-2">THE WOLF CHALLENGE</h1>
      <p id="loading-message" class="text-lg">Loading CTF Platform...</p>
      <div class="mt-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
      </div>
      <div class="mt-4">
        <button id="skip-loading" class="hidden bg-yellow-400 text-black px-4 py-2 font-bold rounded hover:bg-yellow-500">
          SKIP TO LOGIN
        </button>
      </div>
    </div>
  </div>

  <!-- Login Screen -->
  <div id="login-screen" class="hidden fixed inset-0 bg-gray-900 text-white flex items-center justify-center z-40">
    <div class="neo-brutalist bg-white text-black p-8 max-w-md w-full mx-4">
      <div class="text-center mb-6">
        <div class="text-5xl mb-4">🐺</div>
        <h1 class="text-4xl font-bold mb-2">THE WOLF CHALLENGE</h1>
        <p class="text-lg text-gray-600">Capture The Flag Platform</p>
      </div>

      <form id="login-form" class="space-y-4" data-no-csrf="true">
        <div>
          <label class="block text-lg font-bold mb-2">Email</label>
          <input type="email" id="email" required
                 class="neo-brutalist w-full p-3 text-lg bg-white border-4 border-black focus:outline-none"
                 placeholder="Enter your authorized email">
        </div>
        <div>
          <label class="block text-lg font-bold mb-2">Password</label>
          <input type="password" id="password" required
                 class="neo-brutalist w-full p-3 text-lg bg-white border-4 border-black focus:outline-none"
                 placeholder="Enter your secure password">
        </div>
        <button type="submit"
                class="neo-brutalist w-full bg-red-500 text-white text-lg font-bold py-3 px-6 hover:bg-red-600">
          <i class="fas fa-sign-in-alt mr-2"></i>LOGIN TO HUNT
        </button>

        <!-- Security Notice -->
        <div class="mt-4 p-3 bg-red-100 border-4 border-red-500 text-red-800">
          <i class="fas fa-shield-alt mr-2"></i>
          <strong>Secure Access Only:</strong> Only authorized participant accounts can access this platform.
        </div>

        
      </form>

      <div id="login-error" class="hidden mt-4 p-3 bg-red-100 border-4 border-red-500 text-red-700">
        <i class="fas fa-exclamation-triangle mr-2"></i>
        <span id="login-error-message"></span>
      </div>


    </div>
  </div>

  <!-- Main Application -->
  <div id="main-app" class="hidden">
    <!-- Navigation Header -->
    <nav class="neo-brutalist bg-black text-white p-4 mb-6">
      <div class="container mx-auto flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <div class="logo-container">
            <img src="logo.png" alt="Wolf Logo" class="logo-round" />
          </div>
          <div class="text-2xl">🐺</div>
          <h1 class="text-2xl font-bold text-white">THE WOLF CHALLENGE</h1>
        </div>

        <div class="flex items-center space-x-4">
          <div id="user-info" class="flex items-center space-x-2">
            <i class="fas fa-user"></i>
            <span id="user-email"></span>
            <span id="user-role" class="px-2 py-1 bg-yellow-400 text-black text-sm font-bold"></span>
          </div>
          <button id="logout-btn" class="neo-brutalist bg-red-500 text-white px-4 py-2 text-sm font-bold">
            <i class="fas fa-sign-out-alt mr-1"></i>LOGOUT
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Content Area -->
    <div class="container mx-auto px-4">
      <!-- Dashboard Navigation -->
      <div class="flex flex-wrap gap-4 mb-6">
        <button id="challenges-tab" class="neo-brutalist bg-yellow-400 text-black px-6 py-3 text-lg font-bold">
          <i class="fas fa-flag mr-2"></i>CHALLENGES
        </button>
        <button id="leaderboard-tab" class="neo-brutalist bg-gray-300 text-black px-6 py-3 text-lg font-bold">
          <i class="fas fa-trophy mr-2"></i>LEADERBOARD
        </button>
        <button id="profile-tab" class="neo-brutalist bg-gray-300 text-black px-6 py-3 text-lg font-bold">
          <i class="fas fa-user mr-2"></i>PROFILE
        </button>
        <button id="admin-tab" class="hidden neo-brutalist bg-red-500 text-white px-6 py-3 text-lg font-bold">
          <i class="fas fa-cog mr-2"></i>ADMIN
        </button>
      </div>

      <!-- Content Sections -->
      <div id="content-area">
        <!-- Challenges Section -->
        <div id="challenges-section" class="space-y-6">
          <div class="neo-brutalist bg-white p-6">
            <h2 class="text-3xl font-bold mb-4">🎯 Your Progress</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div class="text-center">
                <div class="text-4xl font-bold text-green-600" id="total-score">0</div>
                <div class="text-lg">Total Score</div>
                <div class="text-sm text-gray-500">/ 700 points</div>
              </div>
              <div class="text-center">
                <div class="text-4xl font-bold text-blue-600" id="challenges-solved">0</div>
                <div class="text-lg">Challenges Solved</div>
                <div class="text-sm text-gray-500">/ 70 challenges</div>
              </div>
              <div class="text-center">
                <div class="text-4xl font-bold text-purple-600" id="current-rank">#-</div>
                <div class="text-lg">Current Rank</div>
                <div class="text-sm text-gray-500">Global</div>
              </div>
              <div class="text-center">
                <div class="text-4xl font-bold text-yellow-600" id="completion-rate">0%</div>
                <div class="text-lg">Completion</div>
                <div class="text-sm text-gray-500">Overall</div>
              </div>
            </div>

            <!-- Detailed Scoring Breakdown -->
            <div class="mt-6 neo-brutalist bg-gray-50 p-4">
              <h3 class="text-lg font-bold mb-3">📊 Scoring Breakdown</h3>
              <div id="scoring-breakdown">
                <!-- Scoring breakdown will be populated here -->
              </div>
            </div>
          </div>

          <!-- Challenge Categories -->
          <div class="space-y-6" id="challenge-categories">
            <!-- Categories will be loaded here -->
          </div>
        </div>

        <!-- Leaderboard Section -->
        <div id="leaderboard-section" class="hidden">
          <!-- Service Fix Panel (hidden by default) -->
          <div id="service-fix-panel" class="hidden mb-4 p-4 bg-yellow-50 border-l-4 border-yellow-400">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-bold text-yellow-800">Service Issue Detected</h4>
                <p class="text-sm text-yellow-700">Click to diagnose and fix connection issues</p>
              </div>
              <button onclick="serviceUnavailableFixer.diagnoseAndFix()"
                      class="neo-brutalist bg-yellow-500 text-white px-4 py-2 font-bold hover:bg-yellow-600">
                🔧 FIX SERVICE
              </button>
            </div>
          </div>

          <div class="neo-brutalist bg-white p-6">
            <h2 class="text-3xl font-bold mb-6">🏆 LEADERBOARD</h2>
            <div id="leaderboard-content">
              <!-- Leaderboard will be loaded here -->
            </div>
          </div>
        </div>

        <!-- Profile Section -->
        <div id="profile-section" class="hidden">
          <div class="neo-brutalist bg-white p-6">
            <h2 class="text-3xl font-bold mb-6">👤 PROFILE</h2>
            <div id="profile-content">
              <!-- Profile will be loaded here -->
            </div>
          </div>
        </div>

        <!-- Admin Section -->
        <div id="admin-section" class="hidden">
          <div class="neo-brutalist bg-white p-6">
            <h2 class="text-3xl font-bold mb-6">⚙️ ADMIN PANEL</h2>
            <div id="admin-content">
              <!-- Admin panel will be loaded here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Challenge Modal -->
  <div id="challenge-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30">
    <div class="neo-brutalist bg-white max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 id="challenge-title" class="text-3xl font-bold"></h3>
          <button id="close-challenge" class="text-2xl font-bold hover:text-red-500">×</button>
        </div>
        <div id="challenge-content">
          <!-- Challenge content will be loaded here -->
        </div>
      </div>
    </div>
  </div>

  <!-- Demo Mode Indicator -->
  <div id="demo-mode-indicator" class="hidden fixed top-4 right-4 neo-brutalist bg-orange-500 text-white px-4 py-2 text-sm font-bold z-40">
    <i class="fas fa-flask mr-2"></i>DEMO MODE
  </div>

  <!-- Developer Credit -->
  <div class="fixed bottom-4 right-4 neo-brutalist bg-yellow-400 text-black px-4 py-2 text-sm font-bold">
    Developed by S.Tamilselvan
  </div>

  <!-- Advanced Security Protection -->
  <script>
    // Advanced Security Features
    (function() {
      'use strict';

      // Disable right-click context menu
      document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        showSecurityAlert('Right-click disabled for security purposes');
        return false;
      });

      // Disable F12, Ctrl+Shift+I, Ctrl+U, Ctrl+S
      document.addEventListener('keydown', function(e) {
        // F12 - Developer Tools
        if (e.keyCode === 123) {
          e.preventDefault();
          showSecurityAlert('Developer tools access denied');
          return false;
        }

        // Ctrl+Shift+I - Developer Tools
        if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
          e.preventDefault();
          showSecurityAlert('Developer tools access denied');
          return false;
        }

        // Ctrl+U - View Source
        if (e.ctrlKey && e.keyCode === 85) {
          e.preventDefault();
          showSecurityAlert('Source code viewing disabled');
          return false;
        }

        // Ctrl+S - Save Page
        if (e.ctrlKey && e.keyCode === 83) {
          e.preventDefault();
          showSecurityAlert('Page saving disabled');
          return false;
        }

        // Ctrl+A - Select All
        if (e.ctrlKey && e.keyCode === 65) {
          e.preventDefault();
          showSecurityAlert('Text selection disabled');
          return false;
        }

        // Ctrl+P - Print
        if (e.ctrlKey && e.keyCode === 80) {
          e.preventDefault();
          showSecurityAlert('Printing disabled');
          return false;
        }

        // Ctrl+Shift+C - Inspect Element
        if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
          e.preventDefault();
          showSecurityAlert('Element inspection disabled');
          return false;
        }

        // Ctrl+Shift+J - Console
        if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
          e.preventDefault();
          showSecurityAlert('Console access denied');
          return false;
        }
      });

      // Disable text selection
      document.addEventListener('selectstart', function(e) {
        e.preventDefault();
        return false;
      });

      // Disable drag and drop
      document.addEventListener('dragstart', function(e) {
        e.preventDefault();
        return false;
      });

      // Disable image saving
      document.addEventListener('dragstart', function(e) {
        if (e.target.tagName === 'IMG') {
          e.preventDefault();
          return false;
        }
      });

      // Clear console periodically
      setInterval(function() {
        console.clear();
        console.log('%c🐺 THE WOLF CHALLENGE - SECURE ACCESS ONLY', 'color: red; font-size: 20px; font-weight: bold;');
        console.log('%cUnauthorized access attempts are logged and monitored.', 'color: orange; font-size: 14px;');
      }, 1000);

      // Detect developer tools
      let devtools = {open: false, orientation: null};
      setInterval(function() {
        if (window.outerHeight - window.innerHeight > 200 ||
            window.outerWidth - window.innerWidth > 200) {
          if (!devtools.open) {
            devtools.open = true;
            showSecurityAlert('Developer tools detected! Access denied.');
            // Redirect to 404 page
            setTimeout(() => {
              window.location.href = 'about:blank';
            }, 2000);
          }
        } else {
          devtools.open = false;
        }
      }, 500);

      // Disable common bypass methods
      window.addEventListener('resize', function() {
        if (window.outerHeight - window.innerHeight > 200) {
          showSecurityAlert('Unauthorized window manipulation detected');
        }
      });

      // Security alert function
      function showSecurityAlert(message) {
        const alert = document.createElement('div');
        alert.style.cssText = `
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: #dc3545;
          color: white;
          padding: 20px;
          border: 4px solid #000;
          box-shadow: 8px 8px 0 #000;
          z-index: 10000;
          font-family: 'Roboto Mono', monospace;
          font-weight: bold;
          text-align: center;
          max-width: 400px;
        `;
        alert.innerHTML = `
          <div style="font-size: 24px; margin-bottom: 10px;">🚫</div>
          <div style="font-size: 16px; margin-bottom: 10px;">${message}</div>
          <div style="font-size: 12px;">Security violation logged</div>
        `;

        document.body.appendChild(alert);

        setTimeout(() => {
          if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
          }
        }, 3000);
      }

      // Make security functions inaccessible
      window.showSecurityAlert = undefined;
      delete window.showSecurityAlert;

    })();
  </script>

  <!-- Loading Management Script -->
  <script>
    // Loading timeout and fallback system
    let loadingTimeout;
    let modulesLoaded = false;

    function updateLoadingMessage(message) {
      const loadingMessage = document.getElementById('loading-message');
      if (loadingMessage) {
        loadingMessage.textContent = message;
      }
    }

    function showSkipButton() {
      const skipButton = document.getElementById('skip-loading');
      if (skipButton) {
        skipButton.classList.remove('hidden');
        skipButton.addEventListener('click', skipToLogin);
      }
    }

    function skipToLogin() {
      console.log('🐺 Proceeding to secure login screen');
      document.getElementById('loading-screen').classList.add('hidden');
      document.getElementById('login-screen').classList.remove('hidden');
    }

    function handleLoadingTimeout() {
      if (!modulesLoaded) {
        console.warn('🐺 Loading timeout - modules may have failed to load');
        updateLoadingMessage('Loading taking longer than expected...');
        showSkipButton();

        // Auto-skip after additional 5 seconds
        setTimeout(() => {
          if (!modulesLoaded) {
            console.log('🐺 Auto-skipping to login after timeout');
            skipToLogin();
          }
        }, 5000);
      }
    }

    // Set loading timeout (10 seconds)
    loadingTimeout = setTimeout(handleLoadingTimeout, 10000);

    // Mark modules as loaded when they finish
    window.markModulesLoaded = function() {
      modulesLoaded = true;
      clearTimeout(loadingTimeout);
    };

    // Fallback: Show login after 15 seconds regardless
    setTimeout(() => {
      if (document.getElementById('loading-screen').style.display !== 'none' &&
          !document.getElementById('loading-screen').classList.contains('hidden')) {
        console.log('🐺 Fallback: Forcing login screen after 15 seconds');
        skipToLogin();
      }
    }, 15000);
  </script>

  <!-- Scripts -->
  <script type="module" src="./firebase-config.js"></script>
  <script type="module" src="./auth.js"></script>
  <script type="module" src="./challenges.js"></script>
  <script type="module" src="./dashboard.js"></script>
  <script type="module" src="./leaderboard.js"></script>
  <script type="module" src="./admin.js"></script>
  <script type="module" src="./security.js"></script>
  <script type="module" src="./script.js"></script>

  <!-- Admin Login Functions -->
  <script>
    // Show admin login modal
    function showAdminLogin() {
      // Remove any existing admin login modal
      const existingModal = document.getElementById('admin-login-modal');
      if (existingModal) {
        existingModal.remove();
      }

      // Create admin login modal
      const modal = document.createElement('div');
      modal.id = 'admin-login-modal';
      modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      modal.innerHTML = `
        <div class="neo-brutalist bg-white p-8 max-w-md mx-4 border-4 border-black">
          <div class="text-center mb-6">
            <div class="text-4xl mb-2">👑</div>
            <h2 class="text-2xl font-bold">Admin Login</h2>
            <p class="text-sm text-gray-600 mt-2">Select admin credentials</p>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-bold mb-2">Admin Email:</label>
              <select id="admin-email-select" class="neo-brutalist w-full p-3 border-4 border-black">
                <option value="<EMAIL>"><EMAIL></option>
                <option value="<EMAIL>"><EMAIL></option>
                <option value="<EMAIL>"><EMAIL></option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-bold mb-2">Password:</label>
              <input type="password" id="admin-password-input"
                     class="neo-brutalist w-full p-3 border-4 border-black"
                     value="WolfChallenge2024!">
            </div>
          </div>

          <div class="mt-6 space-y-3">
            <button onclick="performAdminLogin()"
                    class="neo-brutalist w-full bg-green-500 text-white py-3 font-bold hover:bg-green-600 border-4 border-black">
              🔑 LOGIN AS ADMIN
            </button>

            <div class="flex space-x-2">
              <button onclick="copyAdminCredentials()"
                      class="neo-brutalist flex-1 bg-blue-500 text-white py-2 text-sm font-bold hover:bg-blue-600 border-4 border-black">
                📋 COPY
              </button>
              <button onclick="closeAdminLogin()"
                      class="neo-brutalist flex-1 bg-gray-500 text-white py-2 text-sm font-bold hover:bg-gray-600 border-4 border-black">
                ❌ CLOSE
              </button>
            </div>
          </div>

          <div class="mt-4 p-3 bg-yellow-100 border-4 border-yellow-500 text-sm">
            <strong>💡 Quick Login:</strong><br>
            Select an admin email above, password is auto-filled. Click "LOGIN AS ADMIN".
          </div>
        </div>
      `;

      document.body.appendChild(modal);

      // Update password when email changes
      const emailSelect = document.getElementById('admin-email-select');
      const passwordInput = document.getElementById('admin-password-input');

      const adminPasswords = {
        '<EMAIL>': 'WolfChallenge2024!',
        '<EMAIL>': 'TamilSelvan2024!',
        '<EMAIL>': 'Administrator2024!'
      };

      emailSelect.addEventListener('change', () => {
        passwordInput.value = adminPasswords[emailSelect.value] || 'WolfChallenge2024!';
      });
    }

    // Perform admin login
    async function performAdminLogin() {
      const emailSelect = document.getElementById('admin-email-select');
      const passwordInput = document.getElementById('admin-password-input');

      const email = emailSelect.value;
      const password = passwordInput.value;

      // Fill the main login form
      document.getElementById('email').value = email;
      document.getElementById('password').value = password;

      // Close admin modal
      closeAdminLogin();

      // Show success message
      showTempMessage('Admin credentials filled! Click "LOGIN TO HUNT" to proceed.', 'green');

      // Highlight the main login button
      const loginButton = document.querySelector('button[type="submit"]');
      if (loginButton) {
        loginButton.classList.add('animate-pulse');
        loginButton.style.backgroundColor = '#10B981'; // green
        setTimeout(() => {
          loginButton.classList.remove('animate-pulse');
          loginButton.style.backgroundColor = ''; // reset
        }, 3000);
      }
    }

    // Copy admin credentials
    function copyAdminCredentials() {
      const emailSelect = document.getElementById('admin-email-select');
      const passwordInput = document.getElementById('admin-password-input');

      const credentials = `Email: ${emailSelect.value}\\nPassword: ${passwordInput.value}`;

      navigator.clipboard.writeText(credentials).then(() => {
        showTempMessage('Credentials copied to clipboard!', 'blue');
      }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = credentials;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showTempMessage('Credentials copied to clipboard!', 'blue');
      });
    }

    // Close admin login modal
    function closeAdminLogin() {
      const modal = document.getElementById('admin-login-modal');
      if (modal) {
        modal.remove();
      }
    }

    // Show temporary message
    function showTempMessage(message, color = 'blue') {
      const tempDiv = document.createElement('div');
      tempDiv.className = `fixed top-4 right-4 z-50 neo-brutalist bg-${color}-500 text-white p-3 text-sm border-4 border-black`;
      tempDiv.textContent = message;

      document.body.appendChild(tempDiv);

      setTimeout(() => {
        if (tempDiv.parentElement) {
          tempDiv.remove();
        }
      }, 3000);
    }

    // Close modal when clicking outside
    document.addEventListener('click', (e) => {
      const modal = document.getElementById('admin-login-modal');
      if (modal && e.target === modal) {
        closeAdminLogin();
      }
    });
  </script>
</body>
</html>
