// Enhanced Admin Panel System for The Wolf Challenge
import { db, CTF_CONFIG } from './firebase-config.js';
import authManager from './auth.js';
import scoreService from './score-service.js';
import pushNotificationService from './push-notification-service.js';
import {
  collection,
  doc,
  getDocs,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  limit,
  where,
  serverTimestamp,
  writeBatch
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class AdminManager {
  constructor() {
    this.challenges = [];
    this.users = [];
    this.submissions = [];
    this.currentView = 'overview';
    this.initializeAdmin();
  }

  initializeAdmin() {
    // Only initialize if user is admin
    if (!authManager.isAdmin()) {
      console.log('🐺 User is not admin - admin panel disabled');
      return;
    }

    this.setupAdminTab();
    console.log('🐺 Admin panel initialized');
  }

  setupAdminTab() {
    const adminTab = document.getElementById('admin-tab');
    if (adminTab) {
      adminTab.addEventListener('click', () => {
        this.loadAdminPanel();
      });
    }
  }

  async loadAdminPanel() {
    const adminContent = document.getElementById('admin-content');
    if (!adminContent) return;

    adminContent.innerHTML = `
      <div class="space-y-6">
        <!-- Admin Navigation -->
        <div class="flex flex-wrap gap-2 mb-6">
          <button id="admin-overview" class="admin-nav-btn neo-brutalist bg-red-500 text-white px-4 py-2 text-sm font-bold">
            📊 OVERVIEW
          </button>
          <button id="admin-challenges" class="admin-nav-btn neo-brutalist bg-gray-300 text-black px-4 py-2 text-sm font-bold">
            🎯 CHALLENGES
          </button>
          <button id="admin-users" class="admin-nav-btn neo-brutalist bg-gray-300 text-black px-4 py-2 text-sm font-bold">
            👥 USERS
          </button>
          <button id="admin-submissions" class="admin-nav-btn neo-brutalist bg-gray-300 text-black px-4 py-2 text-sm font-bold">
            📝 SUBMISSIONS
          </button>
          <button id="admin-settings" class="admin-nav-btn neo-brutalist bg-gray-300 text-black px-4 py-2 text-sm font-bold">
            ⚙️ SETTINGS
          </button>
        </div>

        <!-- Admin Content Area -->
        <div id="admin-view-content">
          <div class="text-center py-8">Loading admin panel...</div>
        </div>
      </div>
    `;

    this.setupAdminNavigation();
    this.loadOverview();
  }

  setupAdminNavigation() {
    const navButtons = ['overview', 'challenges', 'users', 'submissions', 'settings'];
    
    navButtons.forEach(view => {
      const button = document.getElementById(`admin-${view}`);
      if (button) {
        button.addEventListener('click', () => {
          this.switchAdminView(view);
        });
      }
    });
  }

  switchAdminView(view) {
    // Update navigation buttons
    document.querySelectorAll('.admin-nav-btn').forEach(btn => {
      btn.className = 'admin-nav-btn neo-brutalist bg-gray-300 text-black px-4 py-2 text-sm font-bold';
    });
    
    const activeBtn = document.getElementById(`admin-${view}`);
    if (activeBtn) {
      activeBtn.className = 'admin-nav-btn neo-brutalist bg-red-500 text-white px-4 py-2 text-sm font-bold';
    }

    this.currentView = view;

    // Load content for the selected view
    switch (view) {
      case 'overview':
        this.loadOverview();
        break;
      case 'challenges':
        this.loadChallengesManagement();
        break;
      case 'users':
        this.loadUsersManagement();
        break;
      case 'submissions':
        this.loadSubmissionsView();
        break;
      case 'settings':
        this.loadSettings();
        break;
    }
  }

  async loadOverview() {
    const content = document.getElementById('admin-view-content');
    if (!content) return;

    try {
      // Load statistics
      const stats = await this.getSystemStats();
      
      content.innerHTML = `
        <div class="space-y-6">
          <h3 class="text-2xl font-bold">📊 System Overview</h3>
          
          <!-- Statistics Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="neo-brutalist bg-blue-50 p-4 text-center">
              <div class="text-3xl font-bold text-blue-600">${stats.totalUsers}</div>
              <div class="text-sm">Total Users</div>
            </div>
            <div class="neo-brutalist bg-green-50 p-4 text-center">
              <div class="text-3xl font-bold text-green-600">${stats.totalChallenges}</div>
              <div class="text-sm">Total Challenges</div>
            </div>
            <div class="neo-brutalist bg-purple-50 p-4 text-center">
              <div class="text-3xl font-bold text-purple-600">${stats.totalSubmissions}</div>
              <div class="text-sm">Total Submissions</div>
            </div>
            <div class="neo-brutalist bg-yellow-50 p-4 text-center">
              <div class="text-3xl font-bold text-yellow-600">${stats.activeUsers}</div>
              <div class="text-sm">Active Users</div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="neo-brutalist bg-white p-6">
            <h4 class="text-xl font-bold mb-4">⚡ Quick Actions</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button onclick="adminManager.createNewChallenge()" 
                      class="neo-brutalist bg-green-500 text-white p-4 text-center font-bold">
                <i class="fas fa-plus mb-2 block text-2xl"></i>
                CREATE CHALLENGE
              </button>
              <button onclick="adminManager.exportData()" 
                      class="neo-brutalist bg-blue-500 text-white p-4 text-center font-bold">
                <i class="fas fa-download mb-2 block text-2xl"></i>
                EXPORT DATA
              </button>
              <button onclick="adminManager.resetLeaderboard()" 
                      class="neo-brutalist bg-red-500 text-white p-4 text-center font-bold">
                <i class="fas fa-refresh mb-2 block text-2xl"></i>
                RESET SCORES
              </button>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="neo-brutalist bg-white p-6">
            <h4 class="text-xl font-bold mb-4">🕒 Recent Activity</h4>
            <div id="recent-admin-activity">
              ${await this.getRecentActivity()}
            </div>
          </div>
        </div>
      `;
    } catch (error) {
      console.error('Error loading admin overview:', error);
      content.innerHTML = '<div class="text-center py-8 text-red-500">Error loading overview</div>';
    }
  }

  async getSystemStats() {
    try {
      const [usersSnap, challengesSnap, submissionsSnap] = await Promise.all([
        getDocs(collection(db, CTF_CONFIG.COLLECTIONS.USERS)),
        getDocs(collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES)),
        getDocs(collection(db, CTF_CONFIG.COLLECTIONS.SUBMISSIONS))
      ]);

      const users = usersSnap.docs.map(doc => doc.data());
      const activeUsers = users.filter(user => {
        if (!user.lastActivity) return false;
        const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return user.lastActivity.toDate() > dayAgo;
      }).length;

      return {
        totalUsers: usersSnap.size,
        totalChallenges: challengesSnap.size,
        totalSubmissions: submissionsSnap.size,
        activeUsers: activeUsers
      };
    } catch (error) {
      console.error('Error getting system stats:', error);
      return {
        totalUsers: 0,
        totalChallenges: 0,
        totalSubmissions: 0,
        activeUsers: 0
      };
    }
  }

  async getRecentActivity() {
    try {
      const submissionsRef = collection(db, CTF_CONFIG.COLLECTIONS.SUBMISSIONS);
      const recentQuery = query(submissionsRef, orderBy('timestamp', 'desc'), limit(10));
      const recentSnap = await getDocs(recentQuery);

      if (recentSnap.empty) {
        return '<div class="text-center py-4 text-gray-500">No recent activity</div>';
      }

      return recentSnap.docs.map(doc => {
        const submission = doc.data();
        const time = submission.timestamp ? submission.timestamp.toDate().toLocaleString() : 'Unknown';
        return `
          <div class="flex justify-between items-center py-2 border-b border-gray-200">
            <div>
              <span class="font-bold">${submission.userId}</span> solved a challenge
            </div>
            <div class="text-sm text-gray-600">${time}</div>
          </div>
        `;
      }).join('');
    } catch (error) {
      console.error('Error getting recent activity:', error);
      return '<div class="text-center py-4 text-red-500">Error loading activity</div>';
    }
  }

  async loadChallengesManagement() {
    const content = document.getElementById('admin-view-content');
    if (!content) return;

    content.innerHTML = `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">🎯 Challenge Management</h3>
          <button onclick="adminManager.showCreateChallengeModal()" 
                  class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold">
            <i class="fas fa-plus mr-2"></i>CREATE CHALLENGE
          </button>
        </div>

        <div id="challenges-list">
          <div class="text-center py-8">Loading challenges...</div>
        </div>
      </div>
    `;

    await this.loadChallengesList();
  }

  async loadChallengesList() {
    try {
      const challengesRef = collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES);
      const challengesSnap = await getDocs(query(challengesRef, orderBy('category'), orderBy('order')));
      
      this.challenges = challengesSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      const challengesList = document.getElementById('challenges-list');
      if (!challengesList) return;

      if (this.challenges.length === 0) {
        challengesList.innerHTML = `
          <div class="text-center py-12">
            <div class="text-4xl mb-4">🎯</div>
            <h4 class="text-xl font-bold mb-2">No Challenges Yet</h4>
            <p class="text-gray-600 mb-4">Create your first challenge to get started</p>
            <button onclick="adminManager.initializeDefaultChallenges()" 
                    class="neo-brutalist bg-blue-500 text-white px-6 py-3 font-bold">
              INITIALIZE DEFAULT CHALLENGES
            </button>
          </div>
        `;
        return;
      }

      // Group challenges by category
      const categories = {
        beginner: [],
        intermediate: [],
        advanced: []
      };

      this.challenges.forEach(challenge => {
        if (categories[challenge.category]) {
          categories[challenge.category].push(challenge);
        }
      });

      challengesList.innerHTML = Object.keys(categories).map(category => {
        const challenges = categories[category];
        if (challenges.length === 0) return '';

        return `
          <div class="neo-brutalist bg-white p-6 mb-4">
            <h4 class="text-xl font-bold mb-4 capitalize">${category} Challenges (${challenges.length})</h4>
            <div class="space-y-2">
              ${challenges.map(challenge => `
                <div class="flex justify-between items-center p-3 bg-gray-50 border-2 border-gray-300">
                  <div class="flex-1">
                    <div class="font-bold">${challenge.title}</div>
                    <div class="text-sm text-gray-600">${challenge.description}</div>
                    <div class="text-xs text-gray-500 mt-1">
                      ${challenge.type} • ${challenge.points} points • Order: ${challenge.order}
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <button onclick="adminManager.editChallenge('${challenge.id}')" 
                            class="neo-brutalist bg-blue-500 text-white px-3 py-1 text-sm font-bold">
                      EDIT
                    </button>
                    <button onclick="adminManager.deleteChallenge('${challenge.id}')" 
                            class="neo-brutalist bg-red-500 text-white px-3 py-1 text-sm font-bold">
                      DELETE
                    </button>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        `;
      }).join('');

    } catch (error) {
      console.error('Error loading challenges:', error);
      const challengesList = document.getElementById('challenges-list');
      if (challengesList) {
        challengesList.innerHTML = '<div class="text-center py-8 text-red-500">Error loading challenges</div>';
      }
    }
  }

  async initializeDefaultChallenges() {
    if (!confirm('This will create default challenges. Continue?')) return;

    try {
      const defaultChallenges = this.getDefaultChallenges();
      
      for (const challenge of defaultChallenges) {
        const challengeRef = doc(collection(db, CTF_CONFIG.COLLECTIONS.CHALLENGES));
        await setDoc(challengeRef, {
          ...challenge,
          createdAt: serverTimestamp(),
          createdBy: authManager.getCurrentUser().uid
        });
      }

      alert('Default challenges created successfully!');
      await this.loadChallengesList();
    } catch (error) {
      console.error('Error creating default challenges:', error);
      alert('Error creating challenges. Please try again.');
    }
  }

  getDefaultChallenges() {
    return [
      // Beginner challenges (10)
      {
        title: "Welcome Hunter",
        description: "Find the hidden flag in the page source",
        category: "beginner",
        type: "HTML/Client-side",
        difficulty: "Easy",
        points: 10,
        order: 1,
        flag: "wolf{welcome_to_the_hunt}",
        instructions: "View the page source to find the hidden flag",
        content: "<!-- wolf{welcome_to_the_hunt} -->",
        hints: ["Try viewing the page source", "Look for HTML comments"]
      },
      {
        title: "Cookie Monster",
        description: "Manipulate cookies to gain access",
        category: "beginner",
        type: "Cookie manipulation",
        difficulty: "Easy",
        points: 10,
        order: 2,
        flag: "wolf{cookie_manipulation_101}",
        instructions: "Change the 'role' cookie value to 'admin'",
        hints: ["Use browser developer tools", "Look at the cookies tab"]
      },
      // Add more challenges here...
      // Intermediate challenges (20)
      {
        title: "SQL Injection Basics",
        description: "Bypass login using SQL injection",
        category: "intermediate",
        type: "Database attacks",
        difficulty: "Medium",
        points: 10,
        order: 1,
        flag: "wolf{sql_injection_master}",
        instructions: "Use SQL injection to bypass the login form",
        hints: ["Try ' OR '1'='1", "Look for vulnerable parameters"]
      },
      // Advanced challenges (40)
      {
        title: "Advanced XSS",
        description: "Execute JavaScript in a filtered environment",
        category: "advanced",
        type: "Web exploitation",
        difficulty: "Hard",
        points: 10,
        order: 1,
        flag: "wolf{xss_filter_bypass}",
        instructions: "Bypass the XSS filter and execute JavaScript",
        hints: ["Try different encoding methods", "Look for filter bypasses"]
      }
    ];
  }

  // Enhanced User Management
  async loadUsersManagement() {
    const content = document.getElementById('admin-view-content');
    if (!content) return;

    content.innerHTML = `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">👥 User Management</h3>
          <div class="flex space-x-2">
            <button onclick="adminManager.exportUsers()"
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-download mr-2"></i>EXPORT USERS
            </button>
            <button onclick="adminManager.showBulkScoreModal()"
                    class="neo-brutalist bg-purple-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-calculator mr-2"></i>BULK SCORE
            </button>
          </div>
        </div>

        <!-- User Filters -->
        <div class="neo-brutalist bg-gray-50 p-4">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <select id="user-role-filter" class="neo-brutalist p-2 bg-white border-2 border-black">
              <option value="all">All Roles</option>
              <option value="participant">Participants</option>
              <option value="admin">Admins</option>
            </select>
            <select id="user-activity-filter" class="neo-brutalist p-2 bg-white border-2 border-black">
              <option value="all">All Users</option>
              <option value="active">Active (24h)</option>
              <option value="recent">Recent (7d)</option>
              <option value="inactive">Inactive</option>
            </select>
            <input type="text" id="user-search" placeholder="Search users..."
                   class="neo-brutalist p-2 bg-white border-2 border-black">
            <button onclick="adminManager.refreshUsers()"
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-refresh mr-1"></i>REFRESH
            </button>
          </div>
        </div>

        <div id="users-list">
          <div class="text-center py-8">Loading users...</div>
        </div>
      </div>
    `;

    await this.loadUsersList();
  }

  async loadUsersList() {
    try {
      const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
      const usersSnap = await getDocs(query(usersRef, orderBy('score', 'desc')));

      this.users = usersSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      const usersList = document.getElementById('users-list');
      if (!usersList) return;

      if (this.users.length === 0) {
        usersList.innerHTML = `
          <div class="text-center py-12">
            <div class="text-4xl mb-4">👥</div>
            <h4 class="text-xl font-bold mb-2">No Users Found</h4>
            <p class="text-gray-600">No users are registered yet</p>
          </div>
        `;
        return;
      }

      usersList.innerHTML = `
        <div class="neo-brutalist bg-white p-6">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead>
                <tr class="border-b-4 border-black bg-gray-100">
                  <th class="text-left p-3 font-bold">User</th>
                  <th class="text-center p-3 font-bold">Role</th>
                  <th class="text-center p-3 font-bold">Score</th>
                  <th class="text-center p-3 font-bold">Solved</th>
                  <th class="text-center p-3 font-bold">Last Active</th>
                  <th class="text-center p-3 font-bold">Actions</th>
                </tr>
              </thead>
              <tbody>
                ${this.users.map(user => this.renderUserRow(user)).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `;

    } catch (error) {
      console.error('Error loading users:', error);
      const usersList = document.getElementById('users-list');
      if (usersList) {
        usersList.innerHTML = '<div class="text-center py-8 text-red-500">Error loading users</div>';
      }
    }
  }

  renderUserRow(user) {
    const lastActive = user.lastActivity ?
      new Date(user.lastActivity.toDate()).toLocaleDateString() : 'Never';

    const roleColor = user.role === 'admin' ? 'bg-red-500' : 'bg-blue-500';

    return `
      <tr class="border-b border-gray-200 hover:bg-gray-50">
        <td class="p-3">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-sm">
              ${user.email ? user.email.charAt(0).toUpperCase() : '?'}
            </div>
            <div>
              <div class="font-bold">${user.email || 'Unknown'}</div>
              <div class="text-sm text-gray-600">ID: ${user.id.substring(0, 8)}...</div>
            </div>
          </div>
        </td>
        <td class="p-3 text-center">
          <span class="px-2 py-1 ${roleColor} text-white text-xs font-bold rounded">
            ${user.role?.toUpperCase() || 'UNKNOWN'}
          </span>
        </td>
        <td class="p-3 text-center">
          <div class="text-lg font-bold text-green-600">${user.score || 0}</div>
        </td>
        <td class="p-3 text-center">
          <div class="text-lg font-bold">${user.challengesSolved || 0}</div>
        </td>
        <td class="p-3 text-center">
          <div class="text-sm">${lastActive}</div>
        </td>
        <td class="p-3 text-center">
          <div class="flex justify-center space-x-2">
            <button onclick="adminManager.showUserDetails('${user.id}')"
                    class="neo-brutalist bg-blue-500 text-white px-2 py-1 text-xs font-bold">
              VIEW
            </button>
            <button onclick="adminManager.showScoreAdjustModal('${user.id}')"
                    class="neo-brutalist bg-yellow-500 text-white px-2 py-1 text-xs font-bold">
              SCORE
            </button>
            ${user.role !== 'admin' ? `
              <button onclick="adminManager.resetUserProgress('${user.id}')"
                      class="neo-brutalist bg-red-500 text-white px-2 py-1 text-xs font-bold">
                RESET
              </button>
            ` : ''}
          </div>
        </td>
      </tr>
    `;
  }

  // Score Management Functions
  async showScoreAdjustModal(userId) {
    const user = this.users.find(u => u.id === userId);
    if (!user) return;

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-6 max-w-md w-full mx-4">
        <h3 class="text-xl font-bold mb-4">Adjust Score for ${user.email}</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-bold mb-2">Current Score</label>
            <div class="text-2xl font-bold text-green-600">${user.score || 0}</div>
          </div>
          <div>
            <label class="block text-sm font-bold mb-2">Score Adjustment</label>
            <input type="number" id="score-adjustment"
                   class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                   placeholder="Enter positive or negative number">
          </div>
          <div>
            <label class="block text-sm font-bold mb-2">Reason</label>
            <textarea id="adjustment-reason"
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black h-20"
                      placeholder="Reason for adjustment..."></textarea>
          </div>
          <div class="flex space-x-2">
            <button onclick="adminManager.applyScoreAdjustment('${userId}')"
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold flex-1">
              APPLY
            </button>
            <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()"
                    class="neo-brutalist bg-gray-500 text-white px-4 py-2 font-bold flex-1">
              CANCEL
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  async applyScoreAdjustment(userId) {
    try {
      const adjustment = parseInt(document.getElementById('score-adjustment').value);
      const reason = document.getElementById('adjustment-reason').value;

      if (isNaN(adjustment) || adjustment === 0) {
        alert('Please enter a valid score adjustment');
        return;
      }

      if (!reason.trim()) {
        alert('Please provide a reason for the adjustment');
        return;
      }

      const currentUser = authManager.getCurrentUser();
      await scoreService.adjustScore(userId, adjustment, reason, currentUser.uid);

      alert('Score adjustment applied successfully!');

      // Close modal and refresh
      document.querySelector('.fixed.inset-0').remove();
      await this.loadUsersList();

    } catch (error) {
      console.error('Error applying score adjustment:', error);
      alert('Error applying score adjustment: ' + error.message);
    }
  }

  // Enhanced Submissions View
  async loadSubmissionsView() {
    const content = document.getElementById('admin-view-content');
    if (!content) return;

    content.innerHTML = `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">📝 Submissions & Activity</h3>
          <div class="flex space-x-2">
            <button onclick="adminManager.exportSubmissions()"
                    class="neo-brutalist bg-blue-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-download mr-2"></i>EXPORT
            </button>
            <button onclick="adminManager.refreshSubmissions()"
                    class="neo-brutalist bg-green-500 text-white px-4 py-2 font-bold">
              <i class="fas fa-refresh mr-2"></i>REFRESH
            </button>
          </div>
        </div>

        <!-- Submission Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="neo-brutalist bg-blue-50 p-4 text-center">
            <div class="text-3xl font-bold text-blue-600" id="total-submissions">0</div>
            <div class="text-sm">Total Submissions</div>
          </div>
          <div class="neo-brutalist bg-green-50 p-4 text-center">
            <div class="text-3xl font-bold text-green-600" id="successful-submissions">0</div>
            <div class="text-sm">Successful</div>
          </div>
          <div class="neo-brutalist bg-purple-50 p-4 text-center">
            <div class="text-3xl font-bold text-purple-600" id="unique-solvers">0</div>
            <div class="text-sm">Unique Solvers</div>
          </div>
          <div class="neo-brutalist bg-yellow-50 p-4 text-center">
            <div class="text-3xl font-bold text-yellow-600" id="avg-solve-time">0</div>
            <div class="text-sm">Avg Solve Time</div>
          </div>
        </div>

        <div id="submissions-list">
          <div class="text-center py-8">Loading submissions...</div>
        </div>
      </div>
    `;

    await this.loadSubmissionsList();
  }

  async loadSubmissionsList() {
    try {
      const submissionsRef = collection(db, CTF_CONFIG.COLLECTIONS.SUBMISSIONS);
      const submissionsSnap = await getDocs(query(submissionsRef, orderBy('timestamp', 'desc'), limit(100)));

      this.submissions = submissionsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Update stats
      document.getElementById('total-submissions').textContent = this.submissions.length;
      document.getElementById('successful-submissions').textContent = this.submissions.filter(s => s.isCorrect !== false).length;
      document.getElementById('unique-solvers').textContent = new Set(this.submissions.map(s => s.userId)).size;

      const submissionsList = document.getElementById('submissions-list');
      if (!submissionsList) return;

      if (this.submissions.length === 0) {
        submissionsList.innerHTML = `
          <div class="text-center py-12">
            <div class="text-4xl mb-4">📝</div>
            <h4 class="text-xl font-bold mb-2">No Submissions Yet</h4>
            <p class="text-gray-600">No challenge submissions found</p>
          </div>
        `;
        return;
      }

      submissionsList.innerHTML = `
        <div class="neo-brutalist bg-white p-6">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead>
                <tr class="border-b-4 border-black bg-gray-100">
                  <th class="text-left p-3 font-bold">Time</th>
                  <th class="text-left p-3 font-bold">User</th>
                  <th class="text-left p-3 font-bold">Challenge</th>
                  <th class="text-center p-3 font-bold">Points</th>
                  <th class="text-center p-3 font-bold">Status</th>
                </tr>
              </thead>
              <tbody>
                ${this.submissions.map(submission => this.renderSubmissionRow(submission)).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `;

    } catch (error) {
      console.error('Error loading submissions:', error);
      const submissionsList = document.getElementById('submissions-list');
      if (submissionsList) {
        submissionsList.innerHTML = '<div class="text-center py-8 text-red-500">Error loading submissions</div>';
      }
    }
  }

  renderSubmissionRow(submission) {
    const timestamp = submission.timestamp ?
      new Date(submission.timestamp.toDate()).toLocaleString() : 'Unknown';

    const statusColor = submission.isCorrect !== false ? 'text-green-600' : 'text-red-600';
    const statusText = submission.isCorrect !== false ? '✅ Correct' : '❌ Incorrect';

    return `
      <tr class="border-b border-gray-200 hover:bg-gray-50">
        <td class="p-3">
          <div class="text-sm">${timestamp}</div>
        </td>
        <td class="p-3">
          <div class="text-sm font-bold">${submission.userId?.substring(0, 8) || 'Unknown'}...</div>
        </td>
        <td class="p-3">
          <div class="text-sm">${submission.challengeId || 'Unknown Challenge'}</div>
        </td>
        <td class="p-3 text-center">
          <div class="font-bold">${submission.points || 0}</div>
        </td>
        <td class="p-3 text-center">
          <div class="${statusColor} font-bold text-sm">${statusText}</div>
        </td>
      </tr>
    `;
  }

  // Enhanced Settings
  async loadSettings() {
    const content = document.getElementById('admin-view-content');
    if (!content) return;

    content.innerHTML = `
      <div class="space-y-6">
        <h3 class="text-2xl font-bold">⚙️ System Settings</h3>

        <!-- Platform Settings -->
        <div class="neo-brutalist bg-white p-6">
          <h4 class="text-xl font-bold mb-4">🏆 Competition Settings</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-bold mb-2">Competition Status</label>
              <select class="neo-brutalist w-full p-3 bg-white border-2 border-black">
                <option>Active</option>
                <option>Paused</option>
                <option>Ended</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-bold mb-2">Registration</label>
              <select class="neo-brutalist w-full p-3 bg-white border-2 border-black">
                <option>Open</option>
                <option>Closed</option>
                <option>Invite Only</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Scoring Settings -->
        <div class="neo-brutalist bg-white p-6">
          <h4 class="text-xl font-bold mb-4">📊 Scoring Configuration</h4>
          <div class="space-y-4">
            <div class="grid grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-bold mb-2">Beginner Points</label>
                <input type="number" value="10" class="neo-brutalist w-full p-3 bg-white border-2 border-black">
              </div>
              <div>
                <label class="block text-sm font-bold mb-2">Intermediate Points</label>
                <input type="number" value="10" class="neo-brutalist w-full p-3 bg-white border-2 border-black">
              </div>
              <div>
                <label class="block text-sm font-bold mb-2">Advanced Points</label>
                <input type="number" value="10" class="neo-brutalist w-full p-3 bg-white border-2 border-black">
              </div>
            </div>
          </div>
        </div>

        <!-- Push Notifications -->
        <div class="neo-brutalist bg-white p-6">
          <h4 class="text-xl font-bold mb-4">📱 Push Notifications</h4>
          <div class="space-y-4">
            <!-- Notification Composer -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-bold mb-2">Notification Title</label>
                <input type="text" id="notification-title"
                       class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                       placeholder="Enter notification title...">
              </div>
              <div>
                <label class="block text-sm font-bold mb-2">Notification Type</label>
                <select id="notification-type" class="neo-brutalist w-full p-3 bg-white border-2 border-black">
                  <option value="admin_message">Admin Message</option>
                  <option value="score_update">Score Update</option>
                  <option value="achievement">Achievement</option>
                  <option value="rank_change">Rank Change</option>
                </select>
              </div>
            </div>
            <div>
              <label class="block text-sm font-bold mb-2">Message</label>
              <textarea id="notification-message"
                        class="neo-brutalist w-full p-3 bg-white border-2 border-black h-24"
                        placeholder="Enter your message..."></textarea>
            </div>
            <div class="flex space-x-4">
              <button onclick="adminManager.sendPushNotificationToAll()"
                      class="neo-brutalist bg-red-500 text-white px-6 py-3 font-bold">
                <i class="fas fa-broadcast-tower mr-2"></i>SEND TO ALL USERS
              </button>
              <button onclick="adminManager.sendTestNotification()"
                      class="neo-brutalist bg-blue-500 text-white px-6 py-3 font-bold">
                <i class="fas fa-test-tube mr-2"></i>TEST NOTIFICATION
              </button>
              <button onclick="adminManager.viewNotificationStats()"
                      class="neo-brutalist bg-purple-500 text-white px-6 py-3 font-bold">
                <i class="fas fa-chart-pie mr-2"></i>VIEW STATS
              </button>
            </div>
          </div>
        </div>

        <!-- System Actions -->
        <div class="neo-brutalist bg-white p-6">
          <h4 class="text-xl font-bold mb-4">🔧 System Actions</h4>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button onclick="adminManager.backupData()"
                    class="neo-brutalist bg-blue-500 text-white p-4 font-bold">
              <i class="fas fa-database mb-2 block text-2xl"></i>
              BACKUP DATA
            </button>
            <button onclick="adminManager.clearCache()"
                    class="neo-brutalist bg-yellow-500 text-white p-4 font-bold">
              <i class="fas fa-broom mb-2 block text-2xl"></i>
              CLEAR CACHE
            </button>
            <button onclick="adminManager.generateReport()"
                    class="neo-brutalist bg-green-500 text-white p-4 font-bold">
              <i class="fas fa-chart-bar mb-2 block text-2xl"></i>
              GENERATE REPORT
            </button>
            <button onclick="adminManager.resetLeaderboard()"
                    class="neo-brutalist bg-red-500 text-white p-4 font-bold">
              <i class="fas fa-refresh mb-2 block text-2xl"></i>
              RESET LEADERBOARD
            </button>
          </div>
        </div>
      </div>
    `;
  }

  // Utility Functions
  async exportUsers() {
    try {
      const csvContent = this.generateUsersCSV();
      this.downloadCSV(csvContent, 'users_export.csv');
    } catch (error) {
      console.error('Error exporting users:', error);
      alert('Error exporting users');
    }
  }

  async exportSubmissions() {
    try {
      const csvContent = this.generateSubmissionsCSV();
      this.downloadCSV(csvContent, 'submissions_export.csv');
    } catch (error) {
      console.error('Error exporting submissions:', error);
      alert('Error exporting submissions');
    }
  }

  generateUsersCSV() {
    const headers = ['ID', 'Email', 'Role', 'Score', 'Challenges Solved', 'Last Activity'];
    const rows = this.users.map(user => [
      user.id,
      user.email || '',
      user.role || '',
      user.score || 0,
      user.challengesSolved || 0,
      user.lastActivity ? new Date(user.lastActivity.toDate()).toISOString() : ''
    ]);

    return [headers, ...rows].map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n');
  }

  generateSubmissionsCSV() {
    const headers = ['ID', 'User ID', 'Challenge ID', 'Points', 'Timestamp', 'Status'];
    const rows = this.submissions.map(submission => [
      submission.id,
      submission.userId || '',
      submission.challengeId || '',
      submission.points || 0,
      submission.timestamp ? new Date(submission.timestamp.toDate()).toISOString() : '',
      submission.isCorrect !== false ? 'Correct' : 'Incorrect'
    ]);

    return [headers, ...rows].map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n');
  }

  downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  async resetUserProgress(userId) {
    if (!confirm('Are you sure you want to reset this user\'s progress? This action cannot be undone.')) {
      return;
    }

    try {
      const userRef = doc(db, CTF_CONFIG.COLLECTIONS.USERS, userId);
      await updateDoc(userRef, {
        score: 0,
        challengesSolved: 0,
        solvedChallenges: [],
        progress: {
          beginner: { solved: 0, total: CTF_CONFIG.SCORING.BEGINNER.challenges },
          intermediate: { solved: 0, total: CTF_CONFIG.SCORING.INTERMEDIATE.challenges },
          advanced: { solved: 0, total: CTF_CONFIG.SCORING.ADVANCED.challenges }
        },
        lastActivity: serverTimestamp()
      });

      alert('User progress reset successfully!');
      await this.loadUsersList();

    } catch (error) {
      console.error('Error resetting user progress:', error);
      alert('Error resetting user progress: ' + error.message);
    }
  }

  async showUserDetails(userId) {
    const user = this.users.find(u => u.id === userId);
    if (!user) return;

    // Get user's score history
    const scoreHistory = await scoreService.getScoreHistory(userId, 20);

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="neo-brutalist bg-white p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold">User Details: ${user.email}</h3>
          <button onclick="this.parentElement.parentElement.parentElement.remove()"
                  class="neo-brutalist bg-red-500 text-white px-3 py-1 font-bold">
            ✕
          </button>
        </div>

        <div class="space-y-4">
          <!-- User Info -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <strong>User ID:</strong> ${user.id}
            </div>
            <div>
              <strong>Role:</strong> ${user.role || 'Unknown'}
            </div>
            <div>
              <strong>Score:</strong> ${user.score || 0}
            </div>
            <div>
              <strong>Challenges Solved:</strong> ${user.challengesSolved || 0}
            </div>
          </div>

          <!-- Progress Breakdown -->
          <div>
            <h4 class="font-bold mb-2">Progress by Category:</h4>
            <div class="space-y-2">
              ${Object.entries(user.progress || {}).map(([category, progress]) => `
                <div class="flex justify-between items-center">
                  <span class="capitalize">${category}:</span>
                  <span>${progress.solved || 0} / ${progress.total || 0}</span>
                </div>
              `).join('')}
            </div>
          </div>

          <!-- Recent Score Events -->
          <div>
            <h4 class="font-bold mb-2">Recent Score Events:</h4>
            <div class="max-h-40 overflow-y-auto">
              ${scoreHistory.length > 0 ? scoreHistory.map(event => `
                <div class="text-sm border-b border-gray-200 py-2">
                  <div class="flex justify-between">
                    <span>${event.type.replace('_', ' ')}</span>
                    <span class="font-bold ${event.pointsAwarded > 0 ? 'text-green-600' : 'text-red-600'}">
                      ${event.pointsAwarded > 0 ? '+' : ''}${event.pointsAwarded}
                    </span>
                  </div>
                  <div class="text-gray-600">
                    ${event.timestamp ? new Date(event.timestamp.toDate()).toLocaleString() : 'Unknown time'}
                  </div>
                </div>
              `).join('') : '<div class="text-gray-500">No score events found</div>'}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  async refreshUsers() {
    await this.loadUsersList();
  }

  async refreshSubmissions() {
    await this.loadSubmissionsList();
  }

  async backupData() {
    try {
      const data = {
        users: this.users,
        submissions: this.submissions,
        challenges: this.challenges,
        timestamp: new Date().toISOString()
      };

      const jsonContent = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `ctf_backup_${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('Data backup completed successfully!');
    } catch (error) {
      console.error('Error backing up data:', error);
      alert('Error creating backup: ' + error.message);
    }
  }

  async clearCache() {
    if (!confirm('Are you sure you want to clear all cached data?')) {
      return;
    }

    try {
      // Clear localStorage
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('ctf_')) {
          localStorage.removeItem(key);
        }
      });

      alert('Cache cleared successfully!');
    } catch (error) {
      console.error('Error clearing cache:', error);
      alert('Error clearing cache: ' + error.message);
    }
  }

  async generateReport() {
    try {
      const report = {
        generatedAt: new Date().toISOString(),
        summary: {
          totalUsers: this.users.length,
          totalSubmissions: this.submissions.length,
          totalChallenges: this.challenges.length,
          activeUsers: this.users.filter(u =>
            u.lastActivity && (new Date() - u.lastActivity.toDate()) < 86400000
          ).length
        },
        topUsers: this.users
          .sort((a, b) => (b.score || 0) - (a.score || 0))
          .slice(0, 10)
          .map(u => ({ email: u.email, score: u.score || 0 })),
        categoryStats: this.calculateCategoryStats()
      };

      const jsonContent = JSON.stringify(report, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `ctf_report_${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('Report generated successfully!');
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Error generating report: ' + error.message);
    }
  }

  calculateCategoryStats() {
    const stats = {
      beginner: { totalSolved: 0, uniqueSolvers: new Set() },
      intermediate: { totalSolved: 0, uniqueSolvers: new Set() },
      advanced: { totalSolved: 0, uniqueSolvers: new Set() }
    };

    this.users.forEach(user => {
      const progress = user.progress || {};
      Object.keys(stats).forEach(category => {
        if (progress[category] && progress[category].solved > 0) {
          stats[category].totalSolved += progress[category].solved;
          stats[category].uniqueSolvers.add(user.id);
        }
      });
    });

    // Convert Sets to counts
    Object.keys(stats).forEach(category => {
      stats[category].uniqueSolvers = stats[category].uniqueSolvers.size;
    });

    return stats;
  }

  // Push Notification Methods
  async sendPushNotificationToAll() {
    const title = document.getElementById('notification-title')?.value;
    const message = document.getElementById('notification-message')?.value;
    const type = document.getElementById('notification-type')?.value;

    if (!title || !message) {
      alert('Please enter both title and message');
      return;
    }

    if (!confirm(`Are you sure you want to send this notification to ALL users?\n\nTitle: ${title}\nMessage: ${message}`)) {
      return;
    }

    try {
      const result = await pushNotificationService.sendNotificationToAll(title, message, { type });

      if (result.success) {
        alert(`Notification sent successfully to ${result.count} users!`);

        // Clear form
        document.getElementById('notification-title').value = '';
        document.getElementById('notification-message').value = '';

        // Log the action
        console.log('🐺 Admin broadcast notification sent:', { title, message, type, count: result.count });
      }
    } catch (error) {
      console.error('🐺 Error sending push notification:', error);
      alert('Error sending notification: ' + error.message);
    }
  }

  async sendTestNotification() {
    try {
      await pushNotificationService.sendTestNotification();
      alert('Test notification sent! Check your browser notifications.');
    } catch (error) {
      console.error('🐺 Error sending test notification:', error);
      alert('Error sending test notification: ' + error.message);
    }
  }

  async viewNotificationStats() {
    try {
      const subscriptions = await pushNotificationService.getAllSubscriptions();
      const activeSubscriptions = subscriptions.filter(sub => sub.isActive);

      const modal = document.createElement('div');
      modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      modal.innerHTML = `
        <div class="neo-brutalist bg-white p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold">📱 Push Notification Statistics</h3>
            <button onclick="this.parentElement.parentElement.parentElement.remove()"
                    class="neo-brutalist bg-red-500 text-white px-3 py-1 font-bold">
              ✕
            </button>
          </div>

          <div class="space-y-4">
            <!-- Stats Overview -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div class="neo-brutalist bg-blue-50 p-3 text-center">
                <div class="text-2xl font-bold text-blue-600">${subscriptions.length}</div>
                <div class="text-xs">Total Subscriptions</div>
              </div>
              <div class="neo-brutalist bg-green-50 p-3 text-center">
                <div class="text-2xl font-bold text-green-600">${activeSubscriptions.length}</div>
                <div class="text-xs">Active Subscriptions</div>
              </div>
              <div class="neo-brutalist bg-yellow-50 p-3 text-center">
                <div class="text-2xl font-bold text-yellow-600">${subscriptions.length - activeSubscriptions.length}</div>
                <div class="text-xs">Inactive</div>
              </div>
              <div class="neo-brutalist bg-purple-50 p-3 text-center">
                <div class="text-2xl font-bold text-purple-600">${Math.round((activeSubscriptions.length / Math.max(subscriptions.length, 1)) * 100)}%</div>
                <div class="text-xs">Active Rate</div>
              </div>
            </div>

            <!-- Recent Subscriptions -->
            <div>
              <h4 class="font-bold mb-2">Recent Subscriptions:</h4>
              <div class="max-h-40 overflow-y-auto">
                ${subscriptions.slice(0, 10).map(sub => `
                  <div class="text-sm border-b border-gray-200 py-2 flex justify-between">
                    <span>User: ${sub.userId.substring(0, 8)}...</span>
                    <span class="${sub.isActive ? 'text-green-600' : 'text-red-600'}">
                      ${sub.isActive ? '✅ Active' : '❌ Inactive'}
                    </span>
                  </div>
                `).join('')}
              </div>
            </div>

            <!-- Actions -->
            <div class="flex space-x-2">
              <button onclick="adminManager.exportNotificationData()"
                      class="neo-brutalist bg-blue-500 text-white px-4 py-2 text-sm font-bold">
                EXPORT DATA
              </button>
              <button onclick="adminManager.cleanupInactiveSubscriptions()"
                      class="neo-brutalist bg-red-500 text-white px-4 py-2 text-sm font-bold">
                CLEANUP INACTIVE
              </button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
    } catch (error) {
      console.error('🐺 Error getting notification stats:', error);
      alert('Error loading notification statistics: ' + error.message);
    }
  }

  async exportNotificationData() {
    try {
      const subscriptions = await pushNotificationService.getAllSubscriptions();
      const data = {
        exportedAt: new Date().toISOString(),
        totalSubscriptions: subscriptions.length,
        activeSubscriptions: subscriptions.filter(sub => sub.isActive).length,
        subscriptions: subscriptions.map(sub => ({
          userId: sub.userId,
          isActive: sub.isActive,
          createdAt: sub.createdAt,
          endpoint: sub.endpoint ? 'present' : 'missing'
        }))
      };

      const jsonContent = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `notification_data_${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('Notification data exported successfully!');
    } catch (error) {
      console.error('🐺 Error exporting notification data:', error);
      alert('Error exporting data: ' + error.message);
    }
  }

  async cleanupInactiveSubscriptions() {
    if (!confirm('Are you sure you want to remove all inactive push notification subscriptions?')) {
      return;
    }

    try {
      // This would typically be implemented server-side
      alert('Cleanup functionality would be implemented server-side for security.');
    } catch (error) {
      console.error('🐺 Error cleaning up subscriptions:', error);
      alert('Error during cleanup: ' + error.message);
    }
  }

  async resetLeaderboard() {
    if (!confirm('Are you sure you want to reset the entire leaderboard? This will reset all user scores to 0. This action cannot be undone!')) {
      return;
    }

    const confirmText = prompt('Type "RESET LEADERBOARD" to confirm this action:');
    if (confirmText !== 'RESET LEADERBOARD') {
      alert('Reset cancelled - confirmation text did not match.');
      return;
    }

    try {
      // Reset all user scores
      const usersRef = collection(db, CTF_CONFIG.COLLECTIONS.USERS);
      const usersSnap = await getDocs(usersRef);

      const batch = writeBatch(db);
      let resetCount = 0;

      usersSnap.docs.forEach(doc => {
        const userData = doc.data();
        if (userData.role === CTF_CONFIG.USER_ROLES.PARTICIPANT) {
          batch.update(doc.ref, {
            score: 0,
            challengesSolved: 0,
            solvedChallenges: [],
            progress: {
              beginner: { solved: 0, total: CTF_CONFIG.SCORING.BEGINNER.challenges },
              intermediate: { solved: 0, total: CTF_CONFIG.SCORING.INTERMEDIATE.challenges },
              advanced: { solved: 0, total: CTF_CONFIG.SCORING.ADVANCED.challenges }
            },
            rank: null,
            lastActivity: serverTimestamp()
          });
          resetCount++;
        }
      });

      await batch.commit();

      // Send notification to all users about the reset
      await pushNotificationService.sendNotificationToAll(
        '🔄 Leaderboard Reset',
        'The leaderboard has been reset by an administrator. All scores are now back to zero. Good luck!',
        { type: 'admin_message' }
      );

      alert(`Leaderboard reset successfully! ${resetCount} user scores have been reset to 0.`);

      // Refresh the users list if it's currently displayed
      if (this.currentView === 'users') {
        await this.loadUsersList();
      }

    } catch (error) {
      console.error('🐺 Error resetting leaderboard:', error);
      alert('Error resetting leaderboard: ' + error.message);
    }
  }
}

// Initialize admin manager
const adminManager = new AdminManager();

// Make it globally available for onclick handlers
window.adminManager = adminManager;

export default adminManager;
