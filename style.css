/* The Wolf Challenge CTF Platform Styles - SECURE VERSION */

/* Security Styles */
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* Allow text selection only for input fields */
input, textarea {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Disable image dragging */
img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  pointer-events: none;
}

/* Disable highlighting */
::selection {
  background: transparent;
}

::-moz-selection {
  background: transparent;
}

/* Base Styles */
body {
  font-family: 'Roboto Mono', monospace;
  background-color: #f5f5f5;
  color: #000000;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Enhanced Text Color Consistency */
* {
  color: inherit;
}

/* Ensure all text elements use black color */
h1, h2, h3, h4, h5, h6, p, span, div, a, li, td, th, label {
  color: #000000;
}

/* Link colors - maintain black but add hover effects */
a {
  color: #000000;
  text-decoration: underline;
  transition: opacity 0.2s ease;
}

a:hover {
  opacity: 0.7;
  color: #000000;
}

a:visited {
  color: #000000;
}

/* Neo-Brutalist Design System */
.neo-brutalist {
  border: 4px solid #000;
  box-shadow: 8px 8px 0 #000;
  transition: all 0.2s ease;
  background-color: #fff;
}

.neo-brutalist:hover {
  box-shadow: 6px 6px 0 #000;
  transform: translate(2px, 2px);
}

.neo-brutalist:active {
  box-shadow: 4px 4px 0 #000;
  transform: translate(4px, 4px);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Bebas Neue', sans-serif;
  letter-spacing: 2px;
  font-weight: bold;
}

/* CTF Platform Specific Styles */

/* Challenge Cards */
.challenge-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.challenge-card:hover {
  transform: translate(-2px, -2px);
  box-shadow: 10px 10px 0 #000;
}

.challenge-card.solved {
  background-color: #d4edda !important;
  border-color: #28a745 !important;
}

.challenge-card.solved:hover {
  box-shadow: 10px 10px 0 #28a745;
}

.challenge-card.locked {
  background-color: #f8f9fa !important;
  opacity: 0.6;
  cursor: not-allowed;
}

.challenge-card.locked:hover {
  transform: none;
  box-shadow: 8px 8px 0 #6c757d;
}

/* Progress Bars */
.progress-bar {
  height: 8px;
  background-color: #e9ecef;
  border: 2px solid #000;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.5s ease;
}

/* Leaderboard Styles */
.leaderboard-row {
  transition: background-color 0.2s ease;
}

.leaderboard-row:hover {
  background-color: #f8f9fa;
}

.leaderboard-rank {
  font-weight: bold;
  font-size: 1.2em;
}

/* Modal Enhancements */
.modal-backdrop {
  backdrop-filter: blur(4px);
}

/* Button Variants - Enhanced Text Colors */
.btn-primary {
  background-color: #007bff;
  color: #000000 !important;
  border-color: #000;
}

.btn-primary:hover {
  background-color: #0056b3;
  color: #000000 !important;
}

.btn-success {
  background-color: #28a745;
  color: #000000 !important;
  border-color: #000;
}

.btn-success:hover {
  background-color: #1e7e34;
  color: #000000 !important;
}

.btn-danger {
  background-color: #dc3545;
  color: #000000 !important;
  border-color: #000;
}

.btn-danger:hover {
  background-color: #c82333;
  color: #000000 !important;
}

.btn-warning {
  background-color: #ffc107;
  color: #000000 !important;
  border-color: #000;
}

.btn-warning:hover {
  background-color: #e0a800;
  color: #000000 !important;
}

/* Additional button text enhancement */
button, .btn {
  color: #000000 !important;
}

button:hover, .btn:hover {
  color: #000000 !important;
}

/* Form Enhancements - Enhanced Text Colors */
.form-input {
  border: 4px solid #000;
  padding: 12px;
  font-size: 16px;
  font-family: 'Roboto Mono', monospace;
  background-color: #fff;
  color: #000000 !important;
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  box-shadow: 4px 4px 0 #007bff;
  transform: translate(-2px, -2px);
  color: #000000 !important;
}

.form-input:invalid {
  border-color: #dc3545;
  color: #000000 !important;
}

.form-input:invalid:focus {
  box-shadow: 4px 4px 0 #dc3545;
  color: #000000 !important;
}

/* Additional form element text colors */
input, textarea, select {
  color: #000000 !important;
}

input::placeholder, textarea::placeholder {
  color: #666666 !important;
  opacity: 1;
}

label {
  color: #000000 !important;
  font-weight: bold;
}

/* Loading States */
.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #000;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pulse Animation for Live Updates */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Security Indicators - Enhanced Text Colors */
.security-warning {
  border-left: 8px solid #dc3545;
  background-color: #f8d7da;
  color: #000000 !important;
}

.security-info {
  border-left: 8px solid #007bff;
  background-color: #d1ecf1;
  color: #000000 !important;
}

/* Additional text color enhancements */
.text-black {
  color: #000000 !important;
}

.text-bold-black {
  color: #000000 !important;
  font-weight: bold;
}

/* Table text colors */
table, th, td {
  color: #000000 !important;
}

/* List text colors */
ul, ol, li {
  color: #000000 !important;
}

/* Card text colors */
.card, .card-body, .card-title, .card-text {
  color: #000000 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .neo-brutalist {
    border-width: 3px;
    box-shadow: 6px 6px 0 #000;
  }

  .neo-brutalist:hover {
    box-shadow: 4px 4px 0 #000;
    transform: translate(2px, 2px);
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .challenge-card {
    margin-bottom: 1rem;
  }
}

/* Dark Mode Support - Override to maintain black text */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1a1a1a;
    color: #000000 !important;
  }

  .neo-brutalist {
    background-color: #2d2d2d;
    border-color: #f5f5f5;
    box-shadow: 8px 8px 0 #f5f5f5;
    color: #000000 !important;
  }

  .neo-brutalist:hover {
    box-shadow: 6px 6px 0 #f5f5f5;
  }

  /* Force black text in dark mode */
  *, h1, h2, h3, h4, h5, h6, p, span, div, a, li, td, th, label {
    color: #000000 !important;
  }
}

/* Accessibility Enhancements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus Indicators */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 3px solid #007bff;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .neo-brutalist {
    border-width: 6px;
    box-shadow: 12px 12px 0 #000;
  }

  .challenge-card:hover {
    box-shadow: 15px 15px 0 #000;
  }
}

/* Print Styles */
@media print {
  .neo-brutalist {
    border: 2px solid #000;
    box-shadow: none;
  }

  .challenge-card {
    break-inside: avoid;
  }

  .no-print {
    display: none;
  }
}

/* Enhanced Notification System Styles */
/* Notification animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes notificationPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-slide-out-right {
  animation: slideOutRight 0.3s ease-in;
}

.animate-bounce {
  animation: bounce 1s;
}

.animate-notification-pulse {
  animation: notificationPulse 2s infinite;
}

/* Notification specific styles */
.notification-bell {
  position: relative;
}

.notification-bell::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  opacity: 0;
  animation: notificationPulse 2s infinite;
}

.notification-bell.has-unread::after {
  opacity: 1;
}

/* Enhanced leaderboard animations */
@keyframes rankUp {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: #10b981;
    color: white;
  }
  100% {
    background-color: transparent;
  }
}

@keyframes rankDown {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: #ef4444;
    color: white;
  }
  100% {
    background-color: transparent;
  }
}

.rank-up {
  animation: rankUp 1s ease-in-out;
}

.rank-down {
  animation: rankDown 1s ease-in-out;
}

/* Score animation */
@keyframes scoreIncrease {
  0% {
    transform: scale(1);
    color: inherit;
  }
  50% {
    transform: scale(1.2);
    color: #10b981;
  }
  100% {
    transform: scale(1);
    color: inherit;
  }
}

.score-increase {
  animation: scoreIncrease 0.6s ease-in-out;
}

/* Live update indicators */
@keyframes liveIndicator {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

.live-indicator {
  animation: liveIndicator 2s infinite;
}

/* Notification toast styles */
.notification-toast {
  max-width: 400px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.notification-toast.success {
  border-left: 6px solid #10b981;
}

.notification-toast.info {
  border-left: 6px solid #3b82f6;
}

.notification-toast.warning {
  border-left: 6px solid #f59e0b;
}

.notification-toast.error {
  border-left: 6px solid #ef4444;
}

/* Enhanced progress bars with animations */
@keyframes progressFill {
  from {
    width: 0%;
  }
}

.progress-animated .progress-fill {
  animation: progressFill 1s ease-out;
}

/* Floating score animation */
@keyframes floatingScore {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-40px) scale(1);
    opacity: 0;
  }
}

.floating-score {
  animation: floatingScore 2s ease-out forwards;
  pointer-events: none;
  font-weight: bold;
  color: #000000 !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Additional comprehensive text color enhancements */
/* Navigation text colors */
.nav, .navbar, .nav-link, .navbar-brand {
  color: #000000 !important;
}

/* Modal text colors */
.modal, .modal-header, .modal-body, .modal-footer, .modal-title {
  color: #000000 !important;
}

/* Alert text colors */
.alert {
  color: #000000 !important;
}

/* Badge text colors */
.badge {
  color: #000000 !important;
}

/* Breadcrumb text colors */
.breadcrumb, .breadcrumb-item {
  color: #000000 !important;
}

/* Dropdown text colors */
.dropdown-menu, .dropdown-item {
  color: #000000 !important;
}

/* Pagination text colors */
.pagination, .page-link {
  color: #000000 !important;
}

/* Progress text colors */
.progress-bar {
  color: #000000 !important;
}

/* Tooltip text colors */
.tooltip, .tooltip-inner {
  color: #000000 !important;
}

/* Popover text colors */
.popover, .popover-body, .popover-header {
  color: #000000 !important;
}