/* The Wolf Challenge CTF Platform Styles - SECURE VERSION */

/* Security Styles */
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* Allow text selection only for input fields */
input, textarea {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Disable image dragging */
img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  pointer-events: none;
}

/* Disable highlighting */
::selection {
  background: transparent;
}

::-moz-selection {
  background: transparent;
}

/* Base Styles */
body {
  font-family: 'Roboto Mono', monospace;
  background-color: #f5f5f5;
  color: #000000;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Neo-Brutalist Design System */
.neo-brutalist {
  border: 4px solid #000;
  box-shadow: 8px 8px 0 #000;
  transition: all 0.2s ease;
  background-color: #fff;
}

.neo-brutalist:hover {
  box-shadow: 6px 6px 0 #000;
  transform: translate(2px, 2px);
}

.neo-brutalist:active {
  box-shadow: 4px 4px 0 #000;
  transform: translate(4px, 4px);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Bebas Neue', sans-serif;
  letter-spacing: 2px;
  font-weight: bold;
}

/* CTF Platform Specific Styles */

/* Challenge Cards */
.challenge-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.challenge-card:hover {
  transform: translate(-2px, -2px);
  box-shadow: 10px 10px 0 #000;
}

.challenge-card.solved {
  background-color: #d4edda !important;
  border-color: #28a745 !important;
}

.challenge-card.solved:hover {
  box-shadow: 10px 10px 0 #28a745;
}

.challenge-card.locked {
  background-color: #f8f9fa !important;
  opacity: 0.6;
  cursor: not-allowed;
}

.challenge-card.locked:hover {
  transform: none;
  box-shadow: 8px 8px 0 #6c757d;
}

/* Progress Bars */
.progress-bar {
  height: 8px;
  background-color: #e9ecef;
  border: 2px solid #000;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.5s ease;
}

/* Leaderboard Styles */
.leaderboard-row {
  transition: background-color 0.2s ease;
}

.leaderboard-row:hover {
  background-color: #f8f9fa;
}

.leaderboard-rank {
  font-weight: bold;
  font-size: 1.2em;
}

/* Modal Enhancements */
.modal-backdrop {
  backdrop-filter: blur(4px);
}

/* Button Variants */
.btn-primary {
  background-color: #007bff;
  color: white;
  border-color: #000;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-success {
  background-color: #28a745;
  color: white;
  border-color: #000;
}

.btn-success:hover {
  background-color: #1e7e34;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
  border-color: #000;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-warning {
  background-color: #ffc107;
  color: #000;
  border-color: #000;
}

.btn-warning:hover {
  background-color: #e0a800;
}

/* Form Enhancements */
.form-input {
  border: 4px solid #000;
  padding: 12px;
  font-size: 16px;
  font-family: 'Roboto Mono', monospace;
  background-color: #fff;
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  box-shadow: 4px 4px 0 #007bff;
  transform: translate(-2px, -2px);
}

.form-input:invalid {
  border-color: #dc3545;
}

.form-input:invalid:focus {
  box-shadow: 4px 4px 0 #dc3545;
}

/* Loading States */
.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #000;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pulse Animation for Live Updates */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Security Indicators */
.security-warning {
  border-left: 8px solid #dc3545;
  background-color: #f8d7da;
  color: #721c24;
}

.security-info {
  border-left: 8px solid #007bff;
  background-color: #d1ecf1;
  color: #0c5460;
}

/* Responsive Design */
@media (max-width: 768px) {
  .neo-brutalist {
    border-width: 3px;
    box-shadow: 6px 6px 0 #000;
  }

  .neo-brutalist:hover {
    box-shadow: 4px 4px 0 #000;
    transform: translate(2px, 2px);
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .challenge-card {
    margin-bottom: 1rem;
  }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1a1a1a;
    color: #ffffff;
  }

  .neo-brutalist {
    background-color: #2d2d2d;
    border-color: #f5f5f5;
    box-shadow: 8px 8px 0 #f5f5f5;
  }

  .neo-brutalist:hover {
    box-shadow: 6px 6px 0 #f5f5f5;
  }
}

/* Accessibility Enhancements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus Indicators */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 3px solid #007bff;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .neo-brutalist {
    border-width: 6px;
    box-shadow: 12px 12px 0 #000;
  }

  .challenge-card:hover {
    box-shadow: 15px 15px 0 #000;
  }
}

/* Print Styles */
@media print {
  .neo-brutalist {
    border: 2px solid #000;
    box-shadow: none;
  }

  .challenge-card {
    break-inside: avoid;
  }

  .no-print {
    display: none;
  }
}

/* Enhanced Notification System Styles */
/* Notification animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes notificationPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-slide-out-right {
  animation: slideOutRight 0.3s ease-in;
}

.animate-bounce {
  animation: bounce 1s;
}

.animate-notification-pulse {
  animation: notificationPulse 2s infinite;
}

/* Notification specific styles */
.notification-bell {
  position: relative;
}

.notification-bell::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  opacity: 0;
  animation: notificationPulse 2s infinite;
}

.notification-bell.has-unread::after {
  opacity: 1;
}

/* Enhanced leaderboard animations */
@keyframes rankUp {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: #10b981;
    color: white;
  }
  100% {
    background-color: transparent;
  }
}

@keyframes rankDown {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: #ef4444;
    color: white;
  }
  100% {
    background-color: transparent;
  }
}

.rank-up {
  animation: rankUp 1s ease-in-out;
}

.rank-down {
  animation: rankDown 1s ease-in-out;
}

/* Score animation */
@keyframes scoreIncrease {
  0% {
    transform: scale(1);
    color: inherit;
  }
  50% {
    transform: scale(1.2);
    color: #10b981;
  }
  100% {
    transform: scale(1);
    color: inherit;
  }
}

.score-increase {
  animation: scoreIncrease 0.6s ease-in-out;
}

/* Live update indicators */
@keyframes liveIndicator {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

.live-indicator {
  animation: liveIndicator 2s infinite;
}

/* Notification toast styles */
.notification-toast {
  max-width: 400px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.notification-toast.success {
  border-left: 6px solid #10b981;
}

.notification-toast.info {
  border-left: 6px solid #3b82f6;
}

.notification-toast.warning {
  border-left: 6px solid #f59e0b;
}

.notification-toast.error {
  border-left: 6px solid #ef4444;
}

/* Enhanced progress bars with animations */
@keyframes progressFill {
  from {
    width: 0%;
  }
}

.progress-animated .progress-fill {
  animation: progressFill 1s ease-out;
}

/* Floating score animation */
@keyframes floatingScore {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-40px) scale(1);
    opacity: 0;
  }
}

.floating-score {
  animation: floatingScore 2s ease-out forwards;
  pointer-events: none;
  font-weight: bold;
  color: #10b981;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}